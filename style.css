/* === HEADER === */
.wrapper {
	max-width: 1350px;
	margin: 0 auto;
	width: 100%;
}
header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 100%;
	flex-wrap: wrap;
	background-color: #1a1a1a;
	padding: 20px 0;
}
.menu {
	display: flex;
	list-style-type: none;
	padding: 0;
	gap: 10px;
	position: relative;
	transition: all 0s ease-in 0s;
}
.menu-item {
	text-decoration: none;
	font-weight: 500;
	font-size: 16px;
	padding: 8px 12px;
	color: #ffffff;
}
.menu-item:hover {
	scale: 1.1;
	color: #007bff;
}
.contacts {
	background-color: transparent;
	padding: 15px 0;
}
.contacts__item a,
.contacts__item span {
	color: #ffffff;
	text-decoration: none;
	font-size: 14px;
	font-weight: 400;
}
.contacts__item a:hover {
	text-decoration: underline;
	color: #007bff;
}
.social {
	padding: 25px 0;
	background-color: transparent;
}
.social .wrapper {
	max-width: 180px;
}
.social__buttons {
	display: flex;
	justify-content: center;
	gap: 15px;
}
.social__button > img {
	width: 32px;
	filter: brightness(0) invert(1);
}
.burger-menu {
	display: none;
	flex-direction: column;
	justify-content: space-between;
	width: 30px;
	height: 20px;
	background: transparent;
	border: none;
	cursor: pointer;
	padding: 0;
	z-index: 1001;
}
.burger-menu span {
	width: 100%;
	height: 2px;
	background-color: #ffffff;
	transition: all 0.3s ease;
}
.burger-menu.active span:first-child {
	transform: rotate(45deg) translate(6px, 6px);
}
.burger-menu.active span:nth-child(2) {
	opacity: 0;
}
.burger-menu.active span:last-child {
	transform: rotate(-45deg) translate(6px, -6px);
}
.burger-menu-overlay {
	display: none;
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(26, 26, 26, 0.95);
	z-index: 999;
}

/* === BANNER === */
.banner::before,
.banner::after {
	content: "";
	position: absolute;
	top: 0;
	bottom: 0;
	width: 20%;
	pointer-events: none;
	z-index: 0;
}

.banner::before {
	left: 0;
	background: linear-gradient(
		to right,
		rgba(220, 38, 38, 0.7) 0%,
		rgba(220, 38, 38, 0.5) 30%,
		rgba(220, 38, 38, 0.2) 60%,
		transparent 100%
	);
}

.banner::after {
	right: 0;
	background: linear-gradient(
		to left,
		rgba(220, 38, 38, 0.7) 0%,
		rgba(220, 38, 38, 0.5) 30%,
		rgba(220, 38, 38, 0.2) 60%,
		transparent 100%
	);
}

/* === PRELOADER === */
.preloader {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: #000000;
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 9999;
	transition: opacity 0.5s ease, visibility 0.5s ease;
}

.preloader.fade-out {
	opacity: 0;
	visibility: hidden;
}

.preloader__content {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 100%;
	height: 100%;
}

.preloader__gif {
	max-width: 80%;
	max-height: 80%;
	width: auto;
	height: auto;
	object-fit: contain;
}

/* === BANNER === */
.banner {
	background:
		/* 1️⃣ Вертикальний градієнт (затемнення зверху вниз) */ linear-gradient(
			to bottom,
			#1a1a1a 0%,
			rgba(26, 26, 26, 0.9) 15%,
			rgba(0, 0, 0, 0.6) 40%,
			rgba(0, 0, 0, 0.6) 100%
		),
		/* 2️⃣ Фото */ url("./images/main-bg.jpg") center/cover no-repeat;

	padding: 150px 0;
	position: relative;
	width: 100%;
	color: white;
	overflow: hidden; /* щоб бокові градієнти не виходили за межі */
}
.banner__subtitle {
	font-family: "Raleway", sans-serif;
	font-weight: 400;
	font-size: 18px;
	color: rgba(255, 255, 255, 0.9);
	margin-top: 20px;
	text-align: center;
	max-width: 600px;
	margin-left: auto;
	margin-right: auto;
}
.banner__title {
	font-weight: 600;
	font-size: 48px;
	line-height: 1.2;
	color: #ffffff;
	margin-bottom: 40px;
}
.banner__buttons {
	display: flex;
	gap: 20px;
	justify-content: center;
}
.banner__content {
	margin: 0 auto;
}

/* === WORK PROCESS === */
.work-process {
	padding: 60px 0;
	background: linear-gradient(
		to bottom,
		#ffffff 0%,
		rgba(255, 255, 255, 0.8) 5%,
		rgba(26, 26, 26, 0.8) 95%,
		#1a1a1a 100%
	);
	position: relative;
	width: 100%;
}
.work-process__title {
	font-size: 36px;
	font-weight: 600;
	color: #ffffff;
	text-align: center;
	margin-bottom: 40px;
}
.work-process__grid {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	grid-template-rows: repeat(2, 1fr);
	grid-column-gap: 20px;
	grid-row-gap: 20px;
}
.work-process__grid img {
	width: 100%;
	height: auto;
	aspect-ratio: 1 / 1;
	object-fit: cover;
}
.work-process__item-1 {
	grid-area: 1 / 1 / 2 / 2;
}
.work-process__item-2 {
	grid-area: 2 / 1 / 3 / 2;
}
.work-process__item-3 {
	grid-area: 1 / 2 / 2 / 3;
}
.work-process__item-4 {
	grid-area: 2 / 2 / 3 / 3;
}
.work-process__item-5 {
	grid-area: 1 / 3 / 3 / 5;
}

.work-process__item-1,
.work-process__item-2,
.work-process__item-3,
.work-process__item-4,
.work-process__item-5 {
	position: relative;
	overflow: hidden;
	border-radius: 12px;
}

.work-process__item-1 img,
.work-process__item-2 img,
.work-process__item-3 img,
.work-process__item-4 img,
.work-process__item-5 img {
	width: 100%;
	height: 100%;
	object-fit: cover;
	transition: transform 0.3s ease;
}

.work-process__flip {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(255, 255, 255, 0.9);
	display: flex;
	align-items: center;
	justify-content: center;
	opacity: 0;
	transition: opacity 0.3s ease;
}

.work-process__item-1:hover .work-process__flip,
.work-process__item-2:hover .work-process__flip,
.work-process__item-3:hover .work-process__flip,
.work-process__item-4:hover .work-process__flip,
.work-process__item-5:hover .work-process__flip {
	opacity: 1;
}

.work-process__item-1:hover img,
.work-process__item-2:hover img,
.work-process__item-3:hover img,
.work-process__item-4:hover img,
.work-process__item-5:hover img {
	transform: scale(1.1);
}

.work-process__instagram {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 48px;
	height: 48px;
	background: rgba(51, 51, 51, 0.1);
	border-radius: 50%;
	transition: all 0.3s ease;
}

.work-process__instagram:hover {
	background: rgba(51, 51, 51, 0.2);
	transform: scale(1.1);
}

.work-process__instagram img {
	width: 24px;
	height: 24px;
}

.work-process__more {
	display: flex;
	justify-content: center;
	margin-top: 40px;
}

.work-process__more-button {
	display: flex;
	align-items: center;
	gap: 10px;
	padding: 12px 24px;
	background: rgba(51, 51, 51, 0.1);
	border-radius: 8px;
	color: #000000;
	text-decoration: none;
	font-weight: 500;
	transition: all 0.3s ease;
}

.work-process__more-button:hover {
	background: rgba(51, 51, 51, 0.2);
	transform: scale(1.05);
}

.work-process__more-button img {
	width: 20px;
	height: 20px;
}

/* === SERVICES === */
.services {
	padding: 60px 0;
	background: linear-gradient(
		to bottom,
		#1a1a1a 0%,
		rgba(26, 26, 26, 0.8) 5%,
		rgba(255, 255, 255, 0.8) 95%,
		#ffffff 100%
	);
	width: 100%;
}
.services__title {
	font-size: 32px;
	font-weight: 600;
	color: #000000;
	text-align: center;
	margin-bottom: 40px;
	position: relative;
}
.services .wrapper {
	display: flex;
	flex-direction: column;
	align-items: center;
}
.services__grid {
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;
	align-items: center;
	justify-content: center;
	gap: 20px;
}
.service-card {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
	width: 370px;
	height: 370px;
	border-radius: 12px;
	overflow: hidden;
	text-decoration: none;
	color: inherit;
}
.service-card:hover {
	transform: translateY(-5px);
	box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.service-card__content {
	position: absolute;
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 20px;
	font-size: 20px;
	color: white;
	text-align: center;
	border-radius: 12px;
	background: rgba(0, 0, 0, 0.5);
	z-index: 2;
	transition: background 0.3s ease;
}

.service-card:hover .service-card__content {
	background: rgba(0, 0, 0, 0.7);
}

.service-card:hover .service-card__image {
	transform: scale(1.05);
}
.service-card__image {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	object-fit: cover;
	border-radius: 12px;
	z-index: 0;
	transition: transform 0.3s ease;
}

.service-card__title {
	font-weight: 600;
	font-size: 18px;
	color: white;
	margin: 0;
	z-index: 2;
}

.services {
	position: relative;
}

.services__logo {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	z-index: 0;
	pointer-events: none;
}

.services__logo-image {
	width: 600px;
	height: auto;
	opacity: 0.5;
	filter: brightness(0);
}

.services__grid {
	position: relative;
	z-index: 1;
}
.services__more {
	display: inline-block;
	color: white;
	text-decoration: none;
	font-family: "Raleway", sans-serif;
	font-weight: 500;
	font-size: 14px;
	margin: 0 auto;
	padding: 10px 20px;
	border: 1px solid rgba(255, 255, 255, 0.3);
	border-radius: 5px;
	transition: all 0.3s ease;
}
.services__more:hover {
	background-color: rgba(255, 255, 255, 0.1);
}

/* === SPECIAL OFFER === */
.special-offer {
	padding: 80px 0;
	background: linear-gradient(
		to bottom,
		#ffffff 0%,
		rgba(255, 255, 255, 0.95) 20%,
		rgba(255, 255, 255, 0.95) 80%,
		#ffffff 100%
	), url("./images/special-offer-bg.png");
	background-size: contain;
	position: relative;
	background-repeat: no-repeat;
	background-position: center;
}
.special-offer .wrapper {
	position: relative;
	z-index: 2;
}
.special-offer__content {
	display: flex;
	gap: 35px;
	justify-content: center;
	align-items: center;
	text-align: left;
	margin: 0 auto;
}
.special-offer__title {
	font-size: 35px;
	color: #000000;
	margin-bottom: 20px;
}
.special-offer__discount {
	font-size: 72px;
	margin: 30px 0;
	text-wrap: nowrap;
}
.special-offer__subtitle {
	font-size: 36px;
	margin-bottom: 40px;
}
.special-offer__button {
	background-color: #007bff;
	text-decoration: none;
	padding: 15px 40px;
	border-radius: 5px;
	font-size: 16px;
	color: white;
	height: 55px;
}
.special-offer__button:hover {
	background-color: #0056b3;
}

/* === ACHIEVEMENTS === */
#achievements {
	margin-top: 30px;
	padding: 20px;
	background-image: url("./images/achievements-bg.png");
	background-position: center;
}
.achievements-container {
	display: flex;
	gap: 15px;
	flex-wrap: wrap;
	justify-content: center;
}
.achievements-title {
	font-size: 36px;
	line-height: 40px;
	margin-bottom: 10px;
}
.achievements-subtitle {
	font-size: 20px;
	line-height: 150%;
	font-weight: 400;
	margin-bottom: 80px;
}
.achievement__card {
	background: #007bff;
	height: 190px;
	width: 305px;
	padding: 24px;
	text-align: start;
	border-radius: 15px;
}
.achievement__card_title {
	font-size: 22px;
	line-height: 16px;
}
.achievement__card_number {
	margin-top: 15px;
	font-size: 48px;
	line-height: 36px;
	font-weight: 600;
}
.achievement__card_description {
	margin-top: 30px;
	font-size: 18px;
	font-weight: 400;
}

/* === FAQ === */
.faq {
	padding: 80px 0;
}
.faq__title {
	font-size: 36px;
	font-weight: 600;
	color: #000000;
	text-align: center;
	margin-bottom: 50px;
}
.accordion {
	max-width: 1000px;
	margin: 0 auto;
}
.accordion__item {
	margin-bottom: 15px;
	border-bottom: 2px solid rgba(51, 51, 51, 0.3);
}
.accordion__button {
	width: 100%;
	padding: 20px;
	background: transparent;
	border: none;
	display: flex;
	justify-content: space-between;
	align-items: center;
	cursor: pointer;
	transition: all 0.3s ease;
}
.accordion__button span {
	font-size: 18px;
	color: #000000;
	text-align: left;
}
.accordion__button img {
	width: 24px;
	height: 24px;
	transition: transform 0.3s ease;
}
.accordion__button.active img {
	transform: rotate(180deg);
}
.accordion__content {
	max-height: 0;
	overflow: hidden;
	transition: max-height 0.3s ease;
	padding: 0 20px;
}
.accordion__content p {
	font-family: "Raleway", sans-serif;
	font-size: 16px;
	line-height: 1.6;
	color: rgba(51, 51, 51, 0.9);
	padding-bottom: 20px;
}
.accordion__button:hover {
	background: rgba(51, 51, 51, 0.05);
}
.accordion__item:hover {
	border-color: rgba(51, 51, 51, 0.4);
}

/* === ORDER (FORM) === */
.order-section {
	padding: 60px 0;
	background-color: #1a1a1a;
	width: 100%;
}
.order {
	margin: 0 auto;
	padding: 0 25px;
	max-width: 600px;
	position: relative;
}
.order h2 {
	color: #ffffff;
	margin-bottom: 30px;
}
.form-group {
	margin-bottom: 20px;
}
.form-group label {
	display: block;
	margin-bottom: 5px;
	font-size: 14px;
	text-align: left;
	color: #ffffff;
}
.form-group input {
	width: 100%;
	padding: 10px;
	border: 1px solid #ddd;
	border-radius: 4px;
	font-size: 16px;
}
.form-group input:invalid {
	border-color: #007bff;
}
.modal-submit {
	width: 100%;
	padding: 12px;
	background-color: #007bff;
	color: white;
	border: none;
	border-radius: 4px;
	font-size: 16px;
	cursor: pointer;
	transition: background-color 0.3s;
}
.modal-submit:hover {
	background-color: #0056b3;
}

/* === LOCATION === */
.location {
	padding: 80px 0;
	background-color: #ffffff;
}
.location .wrapper {
	flex-direction: column;
}
.location__title {
	font-size: 36px;
	font-weight: 600;
	color: #000000;
	text-align: center;
	margin-bottom: 50px;
}
.location__content {
	display: flex;
	gap: 40px;
	align-items: flex-start;
	width: 100%;
}
.location__info {
	flex: 0 0 350px;
	display: flex;
	flex-direction: column;
	gap: 30px;
}
.location__item {
	display: flex;
	align-items: flex-start;
	gap: 15px;
	padding: 25px;
	background: linear-gradient(
		180deg,
		rgba(248, 249, 250, 0.8) 0%,
		rgba(233, 236, 239, 0.8) 100%
	);
	border: 1px solid rgba(51, 51, 51, 0.1);
	border-radius: 10px;
	transition: transform 0.3s ease;
}
.location__item:hover {
	transform: translateY(-5px);
}
.location__item img {
	width: 24px;
	height: 24px;
}
.location__text h3 {
	font-family: "Anybody", sans-serif;
	font-size: 18px;
	font-weight: 600;
	color: #007bff;
	margin-bottom: 8px;
	text-align: left;
}
.location__text p,
.location__text a {
	font-family: "Raleway", sans-serif;
	font-size: 16px;
	color: #000000;
	text-decoration: none;
	line-height: 1.4;
}
.location__text a:hover {
	text-decoration: underline;
}
.location__map {
	flex: 1;
	border-radius: 10px;
	overflow: hidden;
	box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
	min-width: 0;
}
.location__map iframe {
	display: block;
	width: 100%;
	height: 450px;
	border: none;
}

/* === FOOTER === */
.footer {
	background-color: #1a1a1a;
	padding: 25px 0;
	margin-top: 0;
	width: 100%;
}
.footer .wrapper {
	justify-content: center;
}
.privacy-policy {
	color: #ffffff;
	text-decoration: none;
	font-size: 14px;
	padding: 8px 20px;
	border: 1px solid rgba(255, 255, 255, 0.3);
	border-radius: 20px;
	transition: background-color 0.3s, border-color 0.3s;
}
.privacy-policy:hover {
	background-color: rgba(255, 255, 255, 0.1);
	border-color: rgba(255, 255, 255, 0.5);
}

/* === SCROLL TOP === */
.scroll-top {
	position: fixed;
	bottom: 30px;
	right: 30px;
	width: 50px;
	height: 50px;
	background: #007bff;
	border: none;
	border-radius: 50%;
	cursor: pointer;
	display: flex;
	align-items: center;
	justify-content: center;
	opacity: 0;
	visibility: hidden;
	transform: translateY(20px);
	transition: all 0.3s ease;
	z-index: 1000;
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}
.scroll-top.visible {
	opacity: 1;
	visibility: visible;
	transform: translateY(0);
}
.scroll-top:hover {
	background: #0056b3;
	transform: translateY(-3px);
}
.scroll-top img {
	width: 24px;
	height: 24px;
}

.button {
	background-color: #007bff;
	color: white;
	padding: 10px 30px;
	border-radius: 5px;
	font-weight: 500;
	font-size: 14px;
	text-decoration: none;
	transition: all 0.3s ease;
}
.button--outlined {
	background-color: rgba(235, 235, 235, 0.5);
	border: 2px solid #007bff;
	color: #000000;
}

@media (max-width: 1200px) {
	.wrapper {
		max-width: 960px;
	}
	.services__grid {
		grid-template-columns: repeat(2, 1fr);
	}
	.work-process__grid {
		grid-template-columns: repeat(2, 1fr);
	}
}
@media (max-width: 992px) {
	.wrapper {
		max-width: 720px;
	}
	.banner__title {
		font-size: 36px;
	}
	.special-offer__title {
		font-size: 32px;
	}
	.special-offer__discount {
		font-size: 56px;
	}
	.location__content {
		flex-direction: column;
	}
	.location__info {
		flex: none;
		width: 100%;
	}
	.burger-menu {
		display: flex;
	}
	.nav {
		position: fixed;
		top: 0;
		left: -100%;
		width: 300px;
		height: 100%;
		background-color: #1a1a1a;
		padding: 100px 30px 30px;
		transition: left 0.3s ease;
		z-index: 1000;
	}
	.nav.active {
		left: 0;
	}
	.menu {
		flex-direction: column;
		background: none;
		padding: 0;
	}
	header .wrapper {
		justify-content: center;
		position: relative;
	}
	.burger-menu {
		position: absolute;
		left: 15px;
	}
	.service-card {
		width: 45%;
		aspect-ratio: 1 / 1;
	}
}
@media (max-width: 768px) {
	.wrapper {
		max-width: 540px;
	}
	.main__logo-container {
		width: -webkit-fill-available;
		display: flex;
		justify-content: center;
		margin-bottom: 50px;
	}
	.services__grid {
		grid-template-columns: 1fr;
	}
	.work-process__grid {
		grid-template-columns: 1fr;
	}
	.banner .wrapper {
		flex-direction: column;
	}
	.banner .wrapper > div:last-child {
		width: 100%;
	}
	.banner .wrapper > div:last-child img {
		max-width: 100%;
		animation: fade-in 1s forwards;
		transform: translateX(0);
	}
	@keyframes fade-in {
		from {
			opacity: 0;
		}
		to {
			opacity: 1;
		}
	}
	.banner__title {
		font-size: 24px;
	}
	.banner__buttons .button {
		margin: 5px 0;
	}
	.special-offer__content {
		flex-direction: column;
	}
	.special-offer__title {
		font-size: 28px;
	}
	.special-offer__discount {
		font-size: 48px;
	}

	.modal-content {
		margin: 15% auto;
		width: 90%;
	}
}
@media (max-width: 576px) {
	.wrapper {
		max-width: 100%;
		padding: 0 15px;
	}
	.main__logo {
		width: 200px;
		height: 40px;
		z-index: 1000;
	}
	.banner__content {
		padding: 50px 0;
	}
	.work-process__title {
		font-size: 24px;
	}
	.burger-menu {
		top: 120px;
	}
	.service-card {
		height: 150px;
		align-items: center;
	}
	.service-card__content {
		align-items: center;
	}
	.services__logo-image {
		width: 400px;
	}
	.preloader__gif {
		max-width: 95%;
		max-height: 95%;
	}
	.service-card__title {
		align-content: center;
		margin: 0;
	}
	.work-process__grid {
		grid-template-columns: repeat(2, 1fr);
		grid-template-rows: repeat(4, auto); /* адаптивно по висоті */
	}

	/* Перепризначення сітки для малих екранів */
	.work-process__item-1 {
		grid-area: 1 / 1 / 2 / 2;
	}
	.work-process__item-2 {
		grid-area: 1 / 2 / 2 / 3;
	}
	.work-process__item-5 {
		grid-area: 2 / 1 / 4 / 3;
	} /* велика картинка */
	.work-process__item-3 {
		grid-area: 4 / 1 / 5 / 2;
	}
	.work-process__item-4 {
		grid-area: 4 / 2 / 5 / 3;
	}
	.social__button {
		width: 25px;
	}
	.achievements {
		padding: 0;
	}

	.achievement__card {
		padding: 15px;
		width: 45%;
		height: 250px;
	}
	.services {
		padding: 30px 0;
	}
	.contacts .wrapper {
		flex-direction: column;
		gap: 15px;
	}
	.contacts {
		margin-left: 50px;
	}
	.banner__title {
		font-size: 22px;
	}
	.services__title {
		font-size: 22px;
	}
	.special-offer__title {
		font-size: 24px;
	}
	.service-card__title {
		font-weight: 400;
	}
	.services__logo-image {
		width: 500px;
	}
	.preloader__gif {
		max-width: 90%;
		max-height: 90%;
	}
	.special-offer__discount {
		font-size: 42px;
	}
	.accordion__button span {
		font-size: 16px;
	}
	.location__item {
		padding: 15px;
	}
	.scroll-top {
		bottom: 20px;
		right: 20px;
		width: 40px;
		height: 40px;
	}
}
